<template>
  <div class="navbar">
    <hamburger
      id="hamburger-container"
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <!-- 下拉选择区域 - 只在指定页面显示 -->
    <dropdown-selector
      v-if="shouldShowDropdownSelector"
      class="dropdown-selector-container"
      @branch-change="handleBranchChange"
      @check-type-change="handleCheckTypeChange"
    />

    <div class="right-menu">
      <template v-if="device !== 'mobile'">
        <search id="header-search" class="right-menu-item" />

        <!-- <error-log class="errLog-container right-menu-item hover-effect" /> -->

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip
          :content="$t('navbar.size')"
          effect="dark"
          placement="bottom"
        >
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
        <lang-select class="right-menu-item hover-effect"></lang-select>
      </template>

      <el-dropdown
        class="avatar-container right-menu-item hover-effect"
        trigger="click"
      >
        <div class="avatar-wrapper">
          <img :src="avatar + '?imageView2/1/w/80/h/80'" class="user-avatar" />
          <span class="user-name">{{ name }}</span>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/profile/index">
            <el-dropdown-item
              ><i class="el-icon-user" />{{
                $t("navbar.profile")
              }}</el-dropdown-item
            >
          </router-link>
          <el-dropdown-item divided @click.native="logout">
            <span style="display: block"
              ><i class="el-icon-switch-button" />{{
                $t("navbar.logOut")
              }}</span
            >
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import DropdownSelector from "@/components/DropdownSelector";
// import ErrorLog from "@/components/ErrorLog";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import Search from "@/components/HeaderSearch";
import LangSelect from "@/components/LangSelect";

export default {
  components: {
    Breadcrumb,
    Hamburger,
    DropdownSelector,
    // ErrorLog,
    Screenfull,
    SizeSelect,
    Search,
    LangSelect,
  },
  computed: {
    ...mapGetters(["sidebar", "userName", "name", "avatar", "device"]),
    // 判断是否应该显示下拉选择器
    shouldShowDropdownSelector() {
      const currentPath = this.$route.path;
      // 检查当前路径是否匹配指定的4个页面
      const targetPages = [
        '/report',        // 报告管理
        '/data-overview', // 报告概览
        '/rules',         // 规则管理
        '/resource-check' // 资源检查
      ];

      // 检查当前路径是否以 /project/:projectId 开头，并且结尾匹配目标页面
      return targetPages.some(page => {
        const pattern = new RegExp(`^/project/[^/]+${page}$`);
        return pattern.test(currentPath);
      });
    },
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    async logout() {
      await this.$store.dispatch("user/logout");
      await this.$store.dispatch("permission/resetState");
      localStorage.setItem("al_userName", this.userName);
      // 只有在本地开发模式下才跳转到登录页面
      if (process.env.VUE_APP_DEPLOY_TYPE === "local") {
        this.$router.push(`/login?redirect=${this.$route.fullPath}`);
      } else {
        // 非本地模式下直接跳转到首页
        this.$router.push("/");
      }
    },
    // 处理分支选择变化
    handleBranchChange(branch) {
      console.log('Navbar接收到分支变化:', branch);
      // 这里可以添加分支变化的业务逻辑
      // 例如：更新store状态、发送API请求等
    },
    // 处理检查类型选择变化
    handleCheckTypeChange(checkType) {
      console.log('Navbar接收到检查类型变化:', checkType);
      // 这里可以添加检查类型变化的业务逻辑
      // 例如：更新store状态、发送API请求等
    },
  },
};
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .dropdown-selector-container {
    float: left;
    margin-left: 16px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        // margin-top: 5px;
        position: relative;
        display: flex;
        text-align: center;
        align-items: center;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          margin-right: 4px;
          border-radius: 50%;
        }
        .user-name {
          cursor: pointer;
          font-weight: 400 !important;
          font-size: 14px;
        }
        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          // top: 25%;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
